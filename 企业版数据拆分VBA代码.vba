Sub 拆分办公用品采购清单()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim currentRow As Long
    Dim outputRow As Long
    Dim cellContent As String
    Dim items() As String
    Dim item As String
    Dim i As Integer
    Dim j As Integer
    Dim itemName As String
    Dim quantity As String
    Dim unit As String
    Dim tempStr As String
    Dim numStart As Integer
    Dim numEnd As Integer
    
    ' 设置工作表
    Set ws = ActiveSheet
    
    ' 清空B、C、D列的现有数据（保留标题行）
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    If lastRow > 1 Then
        ws.Range("B2:D" & lastRow * 10).Clear ' 预留足够空间
    End If
    
    ' 设置输出起始行
    outputRow = 2
    
    ' 遍历A列中的每个单元格（从第2行开始）
    For currentRow = 2 To lastRow
        cellContent = Trim(ws.Cells(currentRow, 1).Value)
        
        ' 如果单元格不为空
        If cellContent <> "" Then
            ' 按中文逗号分割数据
            items = Split(cellContent, "，")
            
            ' 处理每个拆分出的项目
            For i = 0 To UBound(items)
                item = Trim(items(i))
                
                If item <> "" Then
                    ' 提取品名、数量和单位
                    Call 解析办公用品信息(item, itemName, quantity, unit)
                    
                    ' 将结果写入B、C、D列
                    ws.Cells(outputRow, 2).Value = itemName  ' B列：品名
                    ws.Cells(outputRow, 3).Value = unit      ' C列：单位
                    ws.Cells(outputRow, 4).Value = quantity  ' D列：数量
                    
                    ' 移动到下一行
                    outputRow = outputRow + 1
                End If
            Next i
        End If
    Next currentRow
    
    ' 格式化输出区域
    With ws.Range("B1:D" & outputRow - 1)
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With
    
    ' 自动调整列宽
    ws.Columns("B:D").AutoFit
    
    MsgBox "办公用品采购清单拆分完成！共处理了 " & (outputRow - 2) & " 条记录。", vbInformation
End Sub

Sub 解析办公用品信息(inputStr As String, ByRef itemName As String, ByRef quantity As String, ByRef unit As String)
    Dim i As Integer
    Dim char As String
    Dim numStart As Integer
    Dim numEnd As Integer
    Dim tempStr As String
    
    ' 初始化变量
    itemName = ""
    quantity = ""
    unit = ""
    numStart = 0
    numEnd = 0
    
    ' 查找数字开始的位置
    For i = 1 To Len(inputStr)
        char = Mid(inputStr, i, 1)
        If IsNumeric(char) Or char = "." Then
            numStart = i
            Exit For
        End If
    Next i
    
    ' 如果找到了数字开始位置
    If numStart > 0 Then
        ' 提取品名（数字之前的部分）
        itemName = Trim(Left(inputStr, numStart - 1))
        
        ' 查找数字结束的位置
        For i = numStart To Len(inputStr)
            char = Mid(inputStr, i, 1)
            If Not (IsNumeric(char) Or char = ".") Then
                numEnd = i - 1
                Exit For
            End If
        Next i
        
        ' 如果数字一直到字符串末尾
        If numEnd = 0 Then
            numEnd = Len(inputStr)
        End If
        
        ' 提取数量
        quantity = Mid(inputStr, numStart, numEnd - numStart + 1)
        
        ' 提取单位（数字之后的部分）
        If numEnd < Len(inputStr) Then
            unit = Trim(Mid(inputStr, numEnd + 1))
        End If
    Else
        ' 如果没有找到数字，整个字符串作为品名
        itemName = Trim(inputStr)
        quantity = ""
        unit = ""
    End If
    
    ' 数据验证和清理
    If quantity = "" Then quantity = "0"
    If unit = "" Then unit = "个"
    
    ' 确保数量是有效的数字
    If Not IsNumeric(quantity) Then
        quantity = "0"
    End If
End Sub

' 辅助函数：清空输出区域
Sub 清空输出区域()
    Dim ws As Worksheet
    Dim lastRow As Long
    
    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "B").End(xlUp).Row
    
    If lastRow > 1 Then
        ws.Range("B2:D" & lastRow).Clear
        MsgBox "输出区域已清空！", vbInformation
    Else
        MsgBox "输出区域已经是空的！", vbInformation
    End If
End Sub

' 使用说明：
' 1. 确保A列包含办公用品采购清单数据
' 2. 第1行应该是标题行
' 3. 运行"拆分办公用品采购清单"宏即可
' 4. 结果将输出到B列（品名）、C列（单位）、D列（数量）
' 5. 如需重新处理，可先运行"清空输出区域"宏
