# 最终完整VBA数据拆分提示语（确保一次正确输出）

## 完整提示语

**目的**：写VBA代码，将单元格中的并列信息逐条拆解到多个单元格中。

**现状**：
当前工作表第1行是标题行，A列是原始数据。以A2单元格为例，其内容示例如下："打印纸5包，签字笔2盒，订书机1台，文件夹2个"，该单元格包含了4条数据，每条数据间使用中文逗号"，"分隔。每条数据均由品名开始（如"打印纸"），后接数量（如"5"）和单位（如"包"）。

**特殊情况处理要求**：
1. 品名中可能包含字母和数字组合，如"A4纸"、"USB3.0数据线"、"2B铅笔"、"HP1020打印机"等
2. 需要智能识别真正的数量，避免将品名中的数字误认为数量
3. 解析逻辑：从字符串末尾向前查找最后一个独立的数字序列作为数量
4. 判断标准：如果数字前面紧接着字母，则该数字是品名的一部分，继续向前查找

**结果**：
需要从每条数据中提取出品名、单位和数量，并将它们分别填入B列、C列和D列，每条数据占一行。按照此格式处理A列中的所有数据，确保每个数据项正确地按行输出到相应的列。

**核心解析算法**：
```
1. 从字符串末尾开始，向前查找数字
2. 找到数字后，检查数字前一个字符：
   - 如果是字母：继续向前查找下一个数字
   - 如果是中文字符或空格：该数字就是数量
3. 数量确定后：
   - 品名 = 数量之前的所有字符
   - 单位 = 数量之后的所有字符
   - 如果单位为空，默认为"个"
```

**必须正确处理的测试案例**：
- "A4纸6包" → 品名=A4纸，单位=包，数量=6
- "USB3.0数据线1根" → 品名=USB3.0数据线，单位=根，数量=1  
- "2B铅笔5支" → 品名=2B铅笔，单位=支，数量=5
- "HP1020打印机1台" → 品名=HP1020打印机，单位=台，数量=1
- "A4复印纸3包" → 品名=A4复印纸，单位=包，数量=3

**代码结构要求**：
1. 主函数：`拆分办公用品采购清单()`
   - 遍历A列数据
   - 按逗号分割每行数据
   - 调用解析函数处理每个项目
   - 将结果写入B、C、D列
   - 自动格式化输出

2. 解析函数：`解析办公用品信息()`
   - 实现上述核心解析算法
   - 处理各种边界情况
   - 返回品名、单位、数量

3. 辅助函数：`清空输出区域()`
   - 清空B、C、D列数据
   - 便于重新处理

**错误处理要求**：
- 如果无法找到数量，默认为"1"
- 如果无法找到单位，默认为"个"  
- 如果无法找到品名，默认为"未知物品"
- 确保数量是有效数字

**用户体验要求**：
- 处理完成后显示统计信息
- 自动添加边框和格式化
- 自动调整列宽
- 提供清空功能

**关键技术点**：
1. 使用从后往前的查找策略避免品名中数字的干扰
2. 字符类型判断（中文字符、字母、数字）
3. 字符串截取和处理
4. Excel单元格操作和格式化

这个提示语确保生成的VBA代码能够一次性正确处理所有复杂情况，特别是品名中包含数字的特殊案例。
